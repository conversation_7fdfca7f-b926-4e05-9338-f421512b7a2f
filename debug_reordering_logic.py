#!/usr/bin/env python3
"""
调试重新排序逻辑
"""

import numpy as np
from sklearn.preprocessing import StandardScaler

def debug_reordering_logic():
    """调试重新排序逻辑"""
    
    print("=" * 60)
    print("调试重新排序逻辑")
    print("=" * 60)
    
    # 加载数据
    data = np.load('result/2024_1/indicators.npy', allow_pickle=True)
    feature_names = ['节点数', '边数', '平均加权入度', '平均加权出度', '最大强连通分量节点数', '平均强连通分量大小']
    
    print(f"数据形状: {data.shape}")
    print(f"特征名称: {feature_names}")
    
    # 模拟聚类结果（使用简单的K-means来获得初始聚类）
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler
    
    # 标准化数据
    scaler = StandardScaler()
    scaled_data = scaler.fit_transform(data)
    
    # 进行聚类
    n_clusters = 4
    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
    original_labels = kmeans.fit_predict(scaled_data)
    
    # 模拟权重
    weights = np.array([0.167, 0.167, 0.167, 0.167, 0.166, 0.166])
    
    print(f"\n原始聚类分布: {np.bincount(original_labels)}")
    print(f"使用权重: {weights}")
    
    # 执行重新排序逻辑
    def reorder_clusters_by_complexity(data, labels, weights):
        """重新排序聚类"""
        unique_labels = np.unique(labels)
        n_clusters = len(unique_labels)
        
        # 对原始数据进行单独归一化（每个指标归一化到[0,1]）
        normalized_data = np.zeros_like(data)
        for j in range(data.shape[1]):
            col_min = np.min(data[:, j])
            col_max = np.max(data[:, j])
            if col_max > col_min:
                normalized_data[:, j] = (data[:, j] - col_min) / (col_max - col_min)
            else:
                normalized_data[:, j] = 0
        
        print(f"\n归一化数据统计:")
        for j in range(data.shape[1]):
            print(f"  {feature_names[j]}: [{np.min(normalized_data[:, j]):.3f}, {np.max(normalized_data[:, j]):.3f}]")
        
        # 计算每个聚类内所有样本的加权复杂度值的平均值
        cluster_complexity_scores = []
        print(f"\n聚类复杂度分析:")
        
        for label in unique_labels:
            cluster_mask = labels == label
            cluster_data = normalized_data[cluster_mask]
            
            if len(cluster_data) > 0:
                # 计算聚类内每个样本的加权复杂度值
                sample_scores = np.dot(cluster_data, weights)
                # 计算该聚类内所有样本的平均复杂度值
                cluster_avg_score = np.mean(sample_scores)
                
                print(f"  原始聚类 {label}:")
                print(f"    样本数: {len(cluster_data)}")
                print(f"    样本复杂度值范围: [{np.min(sample_scores):.4f}, {np.max(sample_scores):.4f}]")
                print(f"    平均复杂度值: {cluster_avg_score:.4f}")
                
                cluster_complexity_scores.append((label, cluster_avg_score, sample_scores))
            else:
                cluster_complexity_scores.append((label, 0, np.array([])))
        
        # 按复杂度得分排序（从低到高）
        cluster_complexity_scores.sort(key=lambda x: x[1])
        
        print(f"\n排序后的聚类:")
        for new_label, (old_label, avg_score, sample_scores) in enumerate(cluster_complexity_scores):
            print(f"  新等级 {new_label + 1} (原聚类 {old_label}): 平均值 = {avg_score:.4f}")
        
        # 创建标签映射：原标签 -> 新标签（按复杂度等级）
        label_mapping = {}
        complexity_scores = []
        for new_label, (old_label, score, _) in enumerate(cluster_complexity_scores):
            label_mapping[old_label] = new_label
            complexity_scores.append(score)
        
        # 应用标签映射
        reordered_labels = np.array([label_mapping[label] for label in labels])
        
        return reordered_labels, complexity_scores, cluster_complexity_scores
    
    # 执行重新排序
    reordered_labels, complexity_scores, detailed_scores = reorder_clusters_by_complexity(data, original_labels, weights)
    
    print(f"\n重新排序后的分布: {np.bincount(reordered_labels)}")
    
    # 验证重新排序的正确性
    print(f"\n验证重新排序的正确性:")
    
    # 对原始数据进行归一化
    normalized_data = np.zeros_like(data)
    for j in range(data.shape[1]):
        col_min = np.min(data[:, j])
        col_max = np.max(data[:, j])
        if col_max > col_min:
            normalized_data[:, j] = (data[:, j] - col_min) / (col_max - col_min)
        else:
            normalized_data[:, j] = 0
    
    # 计算每个样本的复杂度值
    all_sample_scores = np.dot(normalized_data, weights)
    
    # 按新等级分组检查
    for level in range(n_clusters):
        mask = reordered_labels == level
        if np.any(mask):
            level_scores = all_sample_scores[mask]
            print(f"  等级 {level + 1}: 样本数={np.sum(mask)}, "
                  f"均值={np.mean(level_scores):.4f}, "
                  f"范围=[{np.min(level_scores):.4f}, {np.max(level_scores):.4f}]")
    
    # 检查等级间的重叠情况
    print(f"\n检查等级间重叠:")
    for level1 in range(n_clusters - 1):
        for level2 in range(level1 + 1, n_clusters):
            mask1 = reordered_labels == level1
            mask2 = reordered_labels == level2
            
            if np.any(mask1) and np.any(mask2):
                scores1 = all_sample_scores[mask1]
                scores2 = all_sample_scores[mask2]
                
                max1 = np.max(scores1)
                min2 = np.min(scores2)
                
                if max1 > min2:
                    overlap_count1 = np.sum(scores1 > min2)
                    overlap_count2 = np.sum(scores2 < max1)
                    print(f"  等级 {level1 + 1} 与等级 {level2 + 1} 有重叠:")
                    print(f"    等级 {level1 + 1} 中有 {overlap_count1} 个样本 > 等级 {level2 + 1} 的最小值 {min2:.4f}")
                    print(f"    等级 {level2 + 1} 中有 {overlap_count2} 个样本 < 等级 {level1 + 1} 的最大值 {max1:.4f}")

if __name__ == "__main__":
    debug_reordering_logic()
