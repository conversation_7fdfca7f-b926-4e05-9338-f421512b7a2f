#!/usr/bin/env python3
"""
调试复杂度数据，检查等级与数值的对应关系
"""

import numpy as np
import pickle
from sklearn.preprocessing import MinMaxScaler

def debug_complexity_data():
    """调试复杂度数据"""
    
    print("=" * 60)
    print("调试复杂度数据")
    print("=" * 60)
    
    try:
        # 加载聚类结果
        with open('result/2024_1/cluster_results.pkl', 'rb') as f:
            results = pickle.load(f)
        
        clustering_results = results['clustering_results']
        weight_data = results['weight_analysis_data']
        
        # 获取数据
        complexity_levels = clustering_results['labels'] + 1  # 从0-7变成1-8
        original_data = clustering_results['original_data']
        optimal_weights = weight_data['raw_weights']
        n_clusters = clustering_results['n_clusters']
        
        print(f"聚类数: {n_clusters}")
        print(f"样本总数: {len(complexity_levels)}")
        print(f"最优权重: {optimal_weights}")
        print(f"复杂度等级分布: {np.bincount(complexity_levels)}")
        
        # 重现可视化中的计算逻辑
        n_features = original_data.shape[1]
        
        # 对原始数据进行单独归一化（每个指标归一化到[0,1]）
        normalized_original_data = np.zeros_like(original_data)
        for j in range(n_features):
            col_min = np.min(original_data[:, j])
            col_max = np.max(original_data[:, j])
            if col_max > col_min:
                normalized_original_data[:, j] = (original_data[:, j] - col_min) / (col_max - col_min)
            else:
                normalized_original_data[:, j] = 0
        
        # 计算加权复杂度值
        weighted_complexity = np.dot(normalized_original_data, optimal_weights)
        
        print(f"\n加权复杂度值统计:")
        print(f"  最小值: {np.min(weighted_complexity):.4f}")
        print(f"  最大值: {np.max(weighted_complexity):.4f}")
        print(f"  平均值: {np.mean(weighted_complexity):.4f}")
        print(f"  标准差: {np.std(weighted_complexity):.4f}")
        
        # 按复杂度等级分组分析
        print(f"\n按复杂度等级分组分析:")
        for level in range(1, n_clusters + 1):
            mask = complexity_levels == level
            if np.any(mask):
                level_values = weighted_complexity[mask]
                print(f"  等级 {level}: 样本数={np.sum(mask)}, "
                      f"均值={np.mean(level_values):.4f}, "
                      f"范围=[{np.min(level_values):.4f}, {np.max(level_values):.4f}]")
        
        # 检查是否存在等级与数值不匹配的情况
        print(f"\n检查等级与数值的匹配情况:")
        
        # 计算每个等级的平均值
        level_means = []
        for level in range(1, n_clusters + 1):
            mask = complexity_levels == level
            if np.any(mask):
                level_mean = np.mean(weighted_complexity[mask])
                level_means.append((level, level_mean))
        
        # 检查平均值是否单调递增
        level_means.sort(key=lambda x: x[0])  # 按等级排序
        print("等级 -> 平均复杂度值:")
        for i, (level, mean_val) in enumerate(level_means):
            print(f"  {level} -> {mean_val:.4f}")
            
        # 检查是否单调递增
        is_monotonic = all(level_means[i][1] <= level_means[i+1][1] 
                          for i in range(len(level_means)-1))
        print(f"\n平均值是否单调递增: {is_monotonic}")
        
        if not is_monotonic:
            print("⚠️ 发现问题：复杂度等级与平均值不是单调递增关系！")
            
            # 找出不匹配的等级对
            for i in range(len(level_means)-1):
                if level_means[i][1] > level_means[i+1][1]:
                    print(f"  等级 {level_means[i][0]} (均值={level_means[i][1]:.4f}) > "
                          f"等级 {level_means[i+1][0]} (均值={level_means[i+1][1]:.4f})")
        
        # 随机抽样检查具体数值
        print(f"\n随机抽样检查 (前100个样本):")
        sample_size = min(100, len(complexity_levels))
        for i in range(sample_size):
            if i % 20 == 0:  # 每20个打印一次
                print(f"  样本{i}: 等级={complexity_levels[i]}, 复杂度值={weighted_complexity[i]:.4f}")
        
        # 检查复杂度得分是否与重新排序逻辑一致
        if 'complexity_scores' in clustering_results:
            complexity_scores = clustering_results['complexity_scores']
            print(f"\n重新排序时的复杂度得分:")
            for i, score in enumerate(complexity_scores):
                print(f"  聚类 {i} (等级 {i+1}): 得分 = {score:.4f}")
                
            # 检查得分是否单调递增
            is_scores_monotonic = all(complexity_scores[i] <= complexity_scores[i+1] 
                                    for i in range(len(complexity_scores)-1))
            print(f"复杂度得分是否单调递增: {is_scores_monotonic}")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_complexity_data()
